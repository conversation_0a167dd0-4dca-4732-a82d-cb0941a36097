// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useEffect, useState, useRef } from 'react';
import { View, Text } from 'react-native';
import { Audio } from 'expo-av';
import { VideoCameraIcon } from 'react-native-heroicons/outline';

import { useServerUrl } from '@context/server';
import { useTheme } from '@context/theme';
import { changeOpacity } from '@utils/theme';
import { formatVideoDurationFromMillis } from '@utils/video_duration';
import { isVideo } from '@utils/file';

type VideoMetadataProps = {
    files: FileInfo[];
    isCurrentUser?: boolean;
};

const VideoMetadata: React.FC<VideoMetadataProps> = ({ files, isCurrentUser = false }) => {
    const theme = useTheme();
    const serverUrl = useServerUrl();
    const [videoDuration, setVideoDuration] = useState<number | null>(null);
    const mounted = useRef(false);

    // Find the first video file
    const videoFile = files.find(file => isVideo(file));

    useEffect(() => {
        mounted.current = true;
        return () => {
            mounted.current = false;
        };
    }, []);

    useEffect(() => {
        const getVideoDuration = async () => {
            if (!videoFile?.id) return;

            try {
                const videoUrl = `${serverUrl}/api/v4/files/${videoFile.id}`;
                const { sound } = await Audio.Sound.createAsync(
                    { uri: videoUrl },
                    { shouldPlay: false }
                );
                const status = await sound.getStatusAsync();
                if (status.isLoaded && status.durationMillis) {
                    if (mounted.current) {
                        setVideoDuration(status.durationMillis);
                    }
                }
                await sound.unloadAsync();
            } catch (error) {
                console.log('Could not extract video duration:', error);
            }
        };

        if (videoFile) {
            getVideoDuration();
        }
    }, [videoFile, serverUrl]);

    // Don't render if no video file
    if (!videoFile) {
        return null;
    }

    return (
        <View
            style={{
                flexDirection: 'row',
                alignItems: 'center',
                marginRight: 8,
            }}
        >
            {/* Video Camera Icon */}
            <View
                style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                    right: 150,
                    marginRight: videoDuration ? 6 : 0,
                }}
            >
                <VideoCameraIcon
                    size={25}
                    fill="white"
                />
            </View>

            {/* Duration Display */}
            {videoDuration && (
                <View
                    style={{
                        marginRight: -100,
                        right: 190,
                    }}
                >
                    <Text
                        style={{
                            color: 'white',
                            fontSize: 14,
                            fontFamily: 'IBMPlexSansArabic-Medium',
                            textAlign: 'center',
                            lineHeight: 20,
                        }}
                    >
                        {formatVideoDurationFromMillis(videoDuration)}
                    </Text>
                </View>
            )}
        </View>
    );
};

export default VideoMetadata;
