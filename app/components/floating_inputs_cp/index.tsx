import React, { useState, useRef, useImperativeHandle, forwardRef } from 'react';
import { type GestureResponderEvent, type NativeSyntheticEvent, type StyleProp, type TargetedEvent, Text, TextInput, type TextInputFocusEventData, type TextInputProps, type TextStyle, TouchableWithoutFeedback, View, type ViewStyle } from 'react-native';

import CompassIcon from '@components/compass_icon';
import { changeOpacity, makeStyleSheetFromTheme } from '@utils/theme';

const DEFAULT_INPUT_HEIGHT = 48;
const BORDER_DEFAULT_WIDTH = 1;
const BORDER_FOCUSED_WIDTH = 2;

const getStyleSheet = makeStyleSheetFromTheme((theme) => ({
    container: {
        width: '100%',
    },
    label: {
        color: theme.centerChannelColor,
        fontSize: 16,
        marginBottom: 4,
        fontFamily: 'IBMPlexSansArabic-SemiBold',
        fontWeight: '600',
        
    },
    textInput: {
        flexDirection: 'row',
        textAlign:'right',
        fontFamily: 'IBMPlexSansArabic-Regular',
        fontSize: 16,
        paddingTop: 8,
        paddingBottom: 8,
        paddingHorizontal: 16,        
        color: theme.centerChannelColor,
        borderColor: '#F5F5F5',
        borderRadius: 12,
        borderWidth: BORDER_DEFAULT_WIDTH,
        backgroundColor: theme.centerChannelBg,
    },
    errorContainer: {
        flexDirection: 'row',
        borderColor: 'transparent',
        borderWidth: 1,
    },
    errorIcon: {
        color: theme.errorTextColor,
        fontSize: 14,
        marginRight: 7,
        top: 5,
    },
    errorText: {
        color: theme.errorTextColor,
        fontFamily: 'IBMPlexSansArabic-Regular',
        fontSize: 12,
        lineHeight: 16,
        paddingVertical: 5,
    },
}));

export type FixedLabelTextInputRef = {
    blur: () => void;
    focus: () => void;
    isFocused: () => boolean;
};

type FixedLabelTextInputProps = TextInputProps & {
    containerStyle?: ViewStyle;
    editable?: boolean;
    endAdornment?: React.ReactNode;
    error?: string;
    errorIcon?: string;
    label: string;
    labelTextStyle?: TextStyle;
    multiline?: boolean;
    multilineInputHeight?: number;
    onBlur?: (event: NativeSyntheticEvent<TargetedEvent>) => void;
    onFocus?: (e: NativeSyntheticEvent<TargetedEvent>) => void;
    onPress?: (e: GestureResponderEvent) => void;
    placeholder?: string;
    showErrorIcon?: boolean;
    testID?: string;
    textInputStyle?: TextStyle;
    theme: Theme;
    value?: string;
};

const FixedLabelTextInput = forwardRef<FixedLabelTextInputRef, FixedLabelTextInputProps>(({
    containerStyle,
    editable = true,
    error,
    errorIcon = 'alert-outline',
    endAdornment,
    label = '',
    labelTextStyle,
    multiline,
    multilineInputHeight,
    onBlur,
    onFocus,
    onPress,
    placeholder,
    showErrorIcon = true,
    testID,
    textInputStyle,
    theme,
    value,
    ...props
}: FixedLabelTextInputProps, ref) => {
    const [focused, setIsFocused] = useState(false);
    const inputRef = useRef<TextInput>(null);
    const styles = getStyleSheet(theme);

    useImperativeHandle(ref, () => ({
        blur: () => inputRef.current?.blur(),
        focus: () => inputRef.current?.focus(),
        isFocused: () => inputRef.current?.isFocused() || false,
    }), [inputRef]);

    const onTextInputBlur = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
        setIsFocused(false);
        onBlur?.(e);
    };

    const onTextInputFocus = (e: NativeSyntheticEvent<TextInputFocusEventData>) => {
        setIsFocused(true);
        onFocus?.(e);
    };

    const combinedContainerStyle = [styles.container, containerStyle];
    // const combinedTextInputStyle: StyleProp<TextStyle> = [
    //     styles.textInput,
    //     { borderColor: focused ? theme.buttonBg : changeOpacity(theme.centerChannelColor, 0.16) },
    //     multiline && { height: multilineInputHeight || 100, textAlignVertical: 'top' },
    //     textInputStyle,
    // ];

    return (
        <TouchableWithoutFeedback onPress={onPress}>
            <View style={combinedContainerStyle}>
                <Text style={[styles.label, labelTextStyle]}>
                    {label}
                </Text>
                 <View style={{position: 'relative'}}>
                    <TextInput
                        {...props}
                        editable={editable}
                        style={[styles.textInput, textInputStyle]}
                        placeholder={placeholder}
                        placeholderTextColor={"#64748B"}
                        multiline={multiline}
                        value={value}
                        onFocus={onTextInputFocus}
                        onBlur={onTextInputBlur}
                        ref={inputRef}
                        underlineColorAndroid='transparent'
                        selectionColor={changeOpacity(theme.buttonBg, 0.35)}
                        selectionHandleColor={changeOpacity(theme.buttonBg, 0.6)}
                        cursorColor={theme.buttonBg}
                        testID={testID}
                        autoFocus={props.autoFocus}
                        blurOnSubmit={props.blurOnSubmit}
                        spellCheck={props.spellCheck}
                    />
                    {endAdornment}
                </View> 
                {Boolean(error) && (
                    <View style={styles.errorContainer}>
                        {showErrorIcon && errorIcon &&
                            <CompassIcon
                                name={errorIcon}
                                style={styles.errorIcon}
                            />
                        }
                        <Text style={styles.errorText} testID={`${testID}.error`}>
                            {error}
                        </Text>
                    </View>
                )}
            </View>
        </TouchableWithoutFeedback>
    );
});

FixedLabelTextInput.displayName = 'FixedLabelTextInput';

export default FixedLabelTextInput;
