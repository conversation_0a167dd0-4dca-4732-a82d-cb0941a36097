// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { forwardRef, useCallback, useEffect, useMemo, useState } from 'react';
import { DeviceEventEmitter, Dimensions, Image, Keyboard, type NativeSyntheticEvent, Platform, Pressable, type TextInputFocusEventData, View, type ViewStyle } from 'react-native';
import Animated, { type AnimatedStyleProp } from 'react-native-reanimated';

import Search, { type SearchProps, type SearchRef } from '@components/search';
import { Events } from '@constants';
import { changeOpacity, makeStyleSheetFromTheme } from '@utils/theme';
import { typography } from '@utils/typography';
import { bottomSheet } from '@screens/navigation';
import Header from "../../screens/channel_files/header"
import { FileFilters, type FileFilter } from '@app/utils/file';

import { useIntl } from 'react-intl';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import Badge from '@components/badge';
import CompassIcon from '@components/compass_icon';
import Filter, { DIVIDERS_HEIGHT, FILTER_ITEM_HEIGHT, NUMBER_FILTER_ITEMS } from '@components/files/file_filter';
import { useTheme } from '@context/theme';
import { useIsTablet } from '@hooks/device';
import { TITLE_SEPARATOR_MARGIN, TITLE_SEPARATOR_MARGIN_TABLET, TITLE_HEIGHT } from '@screens/bottom_sheet/content';
import TeamPickerIcon from '@screens/home/<USER>/team_picker_icon';
import { bottomSheetSnapPoint } from '@utils/helpers';
import { TabTypes, type TabType } from '@utils/search';













type Props = SearchProps & {
    topStyle: AnimatedStyleProp<ViewStyle>;
    hideHeader?: () => void;
    theme: Theme;
    isFileSeleceted: boolean,

    selectedFilter?: FileFilter | null;
    changeFilterState?: (state: boolean) => void | null;
       onFilterChanged: (filter: FileFilter) => void ;
    

}

const getStyleSheet = makeStyleSheetFromTheme((theme: Theme) => ({
    container: {
        backgroundColor: theme.sidebarBg,
        paddingHorizontal: 20,
        width: '100%',
        zIndex: 10,
    },
    searchContainer: {
        //display:'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderWidth: 1,
        borderColor: theme.sidebarBg,
        paddingHorizontal: 20,
        width: '100%',
        zIndex: 10,
    },
    inputContainerStyle: {
        // backgroundColor: changeOpacity(theme.sidebarText, 0.12),
    },
    inputStyle: {
        color: theme.sidebarText,
    },
}));

const NavigationSearch = forwardRef<SearchRef, Props>(({
    hideHeader,
    theme,
    topStyle, isFileSeleceted = false,
    changeFilterState,
    selectedFilter,
    onFilterChanged,
    ...searchProps
}: Props, ref) => {
    const intl = useIntl();
    const title = intl.formatMessage({ id: 'screen.search.results.filter.title', defaultMessage: 'Filter by file type' });
    const { bottom } = useSafeAreaInsets();
    const isTablet = useIsTablet();




    const screenWidth = Dimensions.get('window').width - 80;
    const [filter, setFilter] = useState<FileFilter>(FileFilters.ALL);



    const styles = getStyleSheet(theme);

    const cancelButtonProps: SearchProps['cancelButtonProps'] = useMemo(() => ({
        buttonTextStyle: {
            color: changeOpacity(theme.sidebarText, 0.72),
            ...typography('Heading', 100, 'Light'),
        },
        color: theme.sidebarText,
    }), [theme]);

    const onFocus = useCallback((e: NativeSyntheticEvent<TextInputFocusEventData>) => {
        hideHeader?.();
        searchProps.onFocus?.(e);
    }, [hideHeader, searchProps.onFocus]);

    const showEmitter = useCallback(() => {
        if (Platform.OS === 'android') {
            DeviceEventEmitter.emit(Events.TAB_BAR_VISIBLE, false);
        }
    }, []);

    const hideEmitter = useCallback(() => {
        if (Platform.OS === 'android') {
            DeviceEventEmitter.emit(Events.TAB_BAR_VISIBLE, true);
        }
    }, []);

    useEffect(() => {
        const show = Keyboard.addListener('keyboardDidShow', showEmitter);
        const hide = Keyboard.addListener('keyboardDidHide', hideEmitter);

        return () => {
            hide.remove();
            show.remove();
        };
    }, [hideEmitter, showEmitter]);

    const snapPoints = useMemo(() => {
        return [
            1,
            bottomSheetSnapPoint(
                NUMBER_FILTER_ITEMS,
                FILTER_ITEM_HEIGHT,
                bottom,
            ) + TITLE_HEIGHT + DIVIDERS_HEIGHT + (isTablet ? TITLE_SEPARATOR_MARGIN_TABLET : TITLE_SEPARATOR_MARGIN),
        ];
    }, []);
    const handleFilterPress = useCallback(() => {
        const renderContent = () => {
            return (
                <Filter
                    initialFilter={selectedFilter!!}
                    setFilter={onFilterChanged}
                    title={title}
                />
            );
        };
        bottomSheet({
            closeButtonId: 'close-search-filters',
            renderContent,
            snapPoints,
            theme,
            title,
        });
    }, [onFilterChanged, selectedFilter]);




    return (
        <Animated.View style={[
            !isFileSeleceted ?
                styles.container : styles.searchContainer,
            topStyle]}>
           
            <View style={{ width: isFileSeleceted ? screenWidth : '100%' }}>

                <Search
                    {...searchProps}
                    cancelButtonProps={cancelButtonProps}
                    clearIconColor={theme.sidebarText}
                    inputContainerStyle={styles.inputContainerStyle}
                    inputStyle={styles.inputStyle}
                    onFocus={onFocus}
                    placeholderTextColor={changeOpacity(theme.sidebarText, Platform.select({ android: 0.56, default: 0.72 }))}
                    searchIconColor={theme.sidebarText}
                    selectionColor={changeOpacity(theme.buttonBg, 0.35)}
                    ref={ref}
                    testID='navigation.header.search_bar'
                />
            </View>
             {isFileSeleceted &&

                <Pressable
                    onPress={handleFilterPress}

                >
                    <View
                        style={{
                            borderColor: changeOpacity(theme.centerChannelColor, 0.10,),
                            borderWidth: 1,
                            borderRadius: 8,
                            alignItems: 'center',
                            //paddingHorizontal: 12,
                            paddingVertical: 17,
                            width: 40,
                            marginStart: 5

                        }}
                    >
                        <Image
                            source={require("assets/base/images/Subtract.png")}
                            //size={24}
                            style={{
                                tintColor: changeOpacity(theme.centerChannelColor, 0.52),
                                height: 15, width: 15
                            }}
                        />
                    </View>

                </Pressable>

            }
        </Animated.View>
    );
});

NavigationSearch.displayName = 'NavSearch';
export default NavigationSearch;

