// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.
import React from 'react';
import { Dimensions, Pressable, type StyleProp, Text, type TextStyle, View } from 'react-native';

import { useTheme } from '@context/theme';
import { useIsTablet } from '@hooks/device';
import { nonBreakingString } from '@utils/strings';
import { changeOpacity, makeStyleSheetFromTheme } from '@utils/theme';
import { typography } from '@utils/typography';

import CustomStatus from './custom_status';
import { useIntl } from 'react-intl';
import type { PostModel } from '@app/database/models/server';
import { FolderIcon, VideoCameraIcon } from 'react-native-heroicons/outline';

type Props = {
    displayName: string;
    teamDisplayName: string;
    teammateId?: string;
    isMuted: boolean;
    textStyles: StyleProp<TextStyle>;
    testId: string;
    channelName: string;
    lastMessage?: PostModel | undefined
    isShowOnlyState?: boolean | undefined
    hasFile?: boolean | undefined

}

const getStyleSheet = makeStyleSheetFromTheme((theme) => {
    return {
        teamName: {
            color: changeOpacity(theme.centerChannelColor, 0.64),
            ...typography('Heading', 75),
        },
        teamNameMuted: {
            color: changeOpacity(theme.centerChannelColor, 0.32),
        },
        flex: {
            flex: 0,
            flexShrink: 1,

        },
        channelName: {
            ...typography('Heading', 200),
            color: changeOpacity(theme.centerChannelColor, 0.64),
        },
        customStatus: {
            marginLeft: 5,
        },
    };
});
export const ChannelBody = ({
    displayName,
    channelName,
    teamDisplayName,
    teammateId,
    isMuted,
    textStyles,
    testId,
    lastMessage = undefined,
    isShowOnlyState = false,
    hasFile = undefined
}: Props) => {
    const theme = useTheme();
    const styles = getStyleSheet(theme);
    const isTablet = useIsTablet();
    const nonBreakingDisplayName = nonBreakingString(displayName);
    const intl = useIntl();
    const windowWidth = Dimensions.get('window').width;



    // Remove console.log to prevent text rendering issues

    const lastMessageHolder = () => {
        switch (lastMessage == undefined) {
           /* case "Voice":{
                return (
                    <View style={{
                        flexDirection:'row'
                        ,
                    }}>
                        <MicrophoneIcon
                        size={14}
                        color={theme.buttonBg }
                        />
                        <Text
                        style={{
                        fontFamily: 'IBMPlexSansArabic-Regular',
                      marginStart:2,
                        fontSize:12,
                        color:changeOpacity(theme.sidebarText,0.40)
                        }}
                        >
                            {intl.formatMessage({
                            id: 'channel_voice',
                            defaultMessage: 'hannel_voice',
                        })}
                        </Text>
                    </View>
                ) }
             case 'vedio':{
                  return ( <View style={{
                        flexDirection:'row'
                    }}>
                        <MicrophoneIcon
                        size={14}
                        color={theme.buttonBg }
                        />
                        <Text
                        style={{
                        fontFamily: 'IBMPlexSansArabic-Regular',
                      marginStart:2,
                        fontSize:12,
                        color:changeOpacity(theme.sidebarText,0.40)
                        }}
                        >
                            {intl.formatMessage({ id: 'mobile.calls_start_vedio', 
                            defaultMessage: 'Start call' })
  }
                        </Text>
                    </View>)
                }*/    case true:
                return (<Text
                    numberOfLines={1}
                    style={{

                        color: changeOpacity(theme.sidebarText, 0.40),
                        fontSize: 12,
                        fontFamily: 'IBMPlexSansArabic-Regular',
                        textAlign: 'left',
                        left: 2,
                        marginTop: 2,
                        width: windowWidth - 190

                    }}>
                    {''}
                </Text>)
            default:
                switch (hasFile) {
                    case true: {
                        return <FolderIcon size={14}
                            color={changeOpacity(theme.buttonBg, 0.40)} />
                    };
                    default:
                        switch (lastMessage?.type as string) {
                            case 'custom_jitsi': {
                                return <VideoCameraIcon
                                    size={14}
                                    color={changeOpacity(theme.buttonBg, 0.40)}
                                />
                            }

                            default:{
                                 return (
                            <Text
                                numberOfLines={1}
                                style={{
                                    color: changeOpacity(theme.sidebarText, 0.40),
                                    fontSize: 12,
                                    fontFamily: 'IBMPlexSansArabic-Regular',
                                    textAlign: 'left',
                                    left: 2,
                                    // marginTop: 2
                                    width: windowWidth - 165
                                }}>
                                {lastMessage?.message}
                            </Text>
                        )
                            }
                        }
                       
                }


        }
    }


    const handleDisplayName = (nonBreakingDisplay: string) => {


        if (displayName === "Off-Topic")
            return "المجموعة الفرعية"
        else if (displayName === "Town Square") {

            return "المجموعة الرئيسية"

        };
        return nonBreakingDisplay
    }

    const channelText = (
        <View>
            <Text
                ellipsizeMode='tail'
                numberOfLines={1}
                style={[textStyles, {
                    fontSize: 15,
                    width: windowWidth - 170,
                    alignItems: 'flex-end',
                    textAlign: 'left',
                    flexDirection: 'column'
                }]}
                testID={`${testId}.display_name`}
            >
                {
                    handleDisplayName(nonBreakingDisplayName)

                }

                {/*Boolean(channelName) && (
                <Text style={styles.channelName}>
                    {nonBreakingString(` ~${channelName}`)}
                </Text>
            )*/}
            </Text>
        </View>

    );

    if (teamDisplayName) {
        const teamText = (
            <Text
                ellipsizeMode={isTablet ? undefined : 'tail'} // Handled by the parent text on tablets
                numberOfLines={isTablet ? undefined : 1} // Handled by the parent text on tablets
                style={[styles.teamName, isMuted && styles.teamNameMuted, styles.flex]}
                testID={`${testId}.team_display_name`}
            >
                {nonBreakingString(`${isTablet ? ' ' : ''}${teamDisplayName}`)}
                {//nonBreakingString(`${isTablet ? ' ' : ''}${teamDisplayName}`)
                }
            </Text>
        );

        if (isTablet) {
            return (
                <Text
                    ellipsizeMode='tail'
                    numberOfLines={1}
                    style={[
                        textStyles,
                        styles.flex,


                    ]}
                    testID={`${testId}.display_name`}
                >
                    {handleDisplayName(nonBreakingDisplayName)}
                    {
                        //teamText
                    }
                </Text>
            );
        }

        return (
            <View style={[{ flexDirection: 'column', alignItems: 'flex-start', flex: 1 }]}>
                {channelText}
                {teamText}
            </View>
        );
    }


    if (teammateId) {
        const customStatus = (
            <View
                style={{
                    marginBottom: 20
                }}
            >

                <CustomStatus
                    channelText={nonBreakingDisplayName}
                    userId={teammateId}
                    style={styles.customStatus}
                />
            </View>
        );
        return (
            <View
                style={{
                    height: 70,
                    display: 'flex'
                    , flexDirection: 'column',
                    justifyContent: 'center',
                    alignItems: 'flex-start'
                    ,
                    //   backgroundColor: 'red',
                }}
            >

                <View style={[{
                    display: 'flex'
                    , flexDirection: 'row', marginBottom: lastMessage ? 5 : undefined

                }]}>
                    {!isShowOnlyState && channelText}
                    {isShowOnlyState && customStatus}
                </View>
                {//!isShowOnlyState&&lastMessage&&
                    lastMessageHolder()
                }
            </View>
        );
    }

    return channelText;
};
