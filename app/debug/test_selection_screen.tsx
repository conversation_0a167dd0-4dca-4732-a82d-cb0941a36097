// Test screen to verify text selection colors
import React from 'react';
import { View, Text, TextInput, ScrollView, StyleSheet } from 'react-native';
import { useTheme } from '@context/theme';
import { changeOpacity } from '@utils/theme';
import ThemeDebug from './theme_debug';

const TestSelectionScreen = () => {
    const theme = useTheme();
    
    return (
        <ScrollView style={styles.container}>
            <ThemeDebug />
            
            <View style={styles.section}>
                <Text style={styles.sectionTitle}>Test Different TextInput Types:</Text>
                
                <Text style={styles.label}>Single Line Input (Enhanced):</Text>
                <TextInput
                    style={[styles.input, { color: theme.centerChannelColor }]}
                    placeholder="Type and select text here"
                    placeholderTextColor={theme.centerChannelColor}
                    selectionColor={changeOpacity(theme.buttonBg, 0.35)}
                    selectionHandleColor={changeOpacity(theme.buttonBg, 0.6)}
                    cursorColor={theme.buttonBg}
                    defaultValue="Select this text to test enhanced selection color with opacity"
                />
                
                <Text style={styles.label}>Multiline Input (Enhanced):</Text>
                <TextInput
                    style={[styles.input, styles.multilineInput, { color: theme.centerChannelColor }]}
                    placeholder="Type and select text here"
                    placeholderTextColor={theme.centerChannelColor}
                    selectionColor={changeOpacity(theme.buttonBg, 0.35)}
                    selectionHandleColor={changeOpacity(theme.buttonBg, 0.6)}
                    cursorColor={theme.buttonBg}
                    multiline={true}
                    defaultValue="This is a multiline input with enhanced selection styling. Select this text to test the new theme.buttonBg color with 35% opacity for selection highlight, 60% opacity for handles, and full opacity for cursor."
                />
                
                <Text style={styles.label}>Base Color (theme.buttonBg):</Text>
                <View style={[styles.colorSwatch, { backgroundColor: theme.buttonBg }]} />
                <Text style={styles.colorText}>
                    {theme.buttonBg} (Full opacity)
                </Text>

                <Text style={styles.label}>Selection Color (35% opacity):</Text>
                <View style={[styles.colorSwatch, { backgroundColor: changeOpacity(theme.buttonBg, 0.35) }]} />
                <Text style={styles.colorText}>
                    {changeOpacity(theme.buttonBg, 0.35)}
                </Text>

                <Text style={styles.label}>Handle Color (60% opacity):</Text>
                <View style={[styles.colorSwatch, { backgroundColor: changeOpacity(theme.buttonBg, 0.6) }]} />
                <Text style={styles.colorText}>
                    {changeOpacity(theme.buttonBg, 0.6)}
                </Text>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
    },
    section: {
        padding: 20,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 15,
        color: '#000',
    },
    label: {
        fontSize: 14,
        marginBottom: 5,
        marginTop: 10,
        color: '#000',
    },
    input: {
        borderWidth: 1,
        borderColor: '#ccc',
        borderRadius: 4,
        padding: 12,
        fontSize: 16,
        backgroundColor: '#fff',
    },
    multilineInput: {
        height: 80,
        textAlignVertical: 'top',
    },
    colorSwatch: {
        width: 50,
        height: 30,
        borderRadius: 4,
        marginTop: 5,
        borderWidth: 1,
        borderColor: '#ccc',
    },
    colorText: {
        fontSize: 12,
        marginTop: 5,
        color: '#000',
    },
});

export default TestSelectionScreen;
