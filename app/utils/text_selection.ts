// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import { Platform } from 'react-native';
import type { Theme } from '@mm-redux/types/preferences';
import { changeOpacity } from '@utils/theme';

/**
 * Gets the appropriate text selection color for the current platform
 * @param theme - The current theme object
 * @param opacity - The opacity to apply (default: 0.35 for subtle effect)
 * @returns The text selection color to use
 */
export const getTextSelectionColor = (theme: Theme, opacity: number = 0.35): string => {
    // Use theme.buttonBg with opacity for better visual appeal and consistency
    const baseColor = theme.textSelectionColor || theme.buttonBg || '#00987e';
    return changeOpacity(baseColor, opacity);
};

/**
 * Gets text selection properties for TextInput components with enhanced styling
 * @param theme - The current theme object
 * @returns Object with selection color properties with different opacity levels
 */
export const getTextSelectionProps = (theme: Theme) => {
    const baseColor = theme.textSelectionColor || theme.buttonBg || '#00987e';

    return {
        // Text selection highlight with subtle opacity
        selectionColor: changeOpacity(baseColor, 0.35),
        // Selection handles with slightly more opacity for better visibility
        selectionHandleColor: changeOpacity(baseColor, 0.6),
        // Cursor with full opacity for clear visibility
        cursorColor: baseColor,
    };
};

/**
 * Gets enhanced text selection properties with platform-specific optimizations
 * @param theme - The current theme object
 * @param options - Optional configuration for selection styling
 * @returns Object with enhanced selection color properties
 */
export const getEnhancedTextSelectionProps = (theme: Theme, options?: {
    selectionOpacity?: number;
    handleOpacity?: number;
    cursorOpacity?: number;
}) => {
    const baseColor = theme.textSelectionColor || theme.buttonBg || '#00987e';
    const {
        selectionOpacity = 0.35,
        handleOpacity = 0.6,
        cursorOpacity = 1.0
    } = options || {};

    return {
        selectionColor: changeOpacity(baseColor, selectionOpacity),
        selectionHandleColor: changeOpacity(baseColor, handleOpacity),
        cursorColor: changeOpacity(baseColor, cursorOpacity),
    };
};

/**
 * Forces a theme refresh by clearing cached theme data
 * This can be useful when theme properties have been updated
 */
export const forceThemeRefresh = () => {
    // This function can be called to force theme updates
    // Implementation depends on the app's theme caching mechanism
    console.log('Forcing theme refresh for text selection colors');
};
