// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import ArabicNumbers from './englishNumberToArabic';

/**
 * Formats video duration in seconds to MM:SS or HH:MM:SS format
 * Uses Arabic numbers for consistency with the app's locale support
 * 
 * @param durationInSeconds - Duration in seconds
 * @returns Formatted duration string (e.g., "٢:٣٤" or "١:٠٥:٢٢")
 */
export function formatVideoDuration(durationInSeconds: number): string {
    if (!durationInSeconds || durationInSeconds <= 0) {
        return `${ArabicNumbers(0)}:${ArabicNumbers(0).padStart(2, '٠')}`;
    }

    const totalSeconds = Math.floor(durationInSeconds);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    // Format minutes and seconds with leading zeros if needed
    const formattedMinutes = ArabicNumbers(minutes).padStart(2, '٠');
    const formattedSeconds = ArabicNumbers(seconds).padStart(2, '٠');

    if (hours > 0) {
        // Format: HH:MM:SS
        const formattedHours = ArabicNumbers(hours);
        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
    } else {
        // Format: MM:SS
        return `${ArabicNumbers(minutes)}:${formattedSeconds}`;
    }
}

/**
 * Formats video duration in milliseconds to MM:SS or HH:MM:SS format
 * 
 * @param durationInMilliseconds - Duration in milliseconds
 * @returns Formatted duration string
 */
export function formatVideoDurationFromMillis(durationInMilliseconds: number): string {
    const durationInSeconds = durationInMilliseconds / 1000;
    return formatVideoDuration(durationInSeconds);
}
