// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

/**
 * Formats video duration in seconds to MM:SS or HH:MM:SS format
 * Uses standard English digits (0-9) for duration display
 *
 * @param durationInSeconds - Duration in seconds
 * @returns Formatted duration string (e.g., "2:34" or "1:05:22")
 */
export function formatVideoDuration(durationInSeconds: number): string {
    if (!durationInSeconds || durationInSeconds <= 0) {
        return "0:00";
    }

    const totalSeconds = Math.floor(durationInSeconds);
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = totalSeconds % 60;

    // Format minutes and seconds with leading zeros if needed
    const formattedMinutes = minutes.toString().padStart(2, '0');
    const formattedSeconds = seconds.toString().padStart(2, '0');

    if (hours > 0) {
        // Format: HH:MM:SS
        const formattedHours = hours.toString();
        return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
    } else {
        // Format: MM:SS
        return `${minutes}:${formattedSeconds}`;
    }
}

/**
 * Formats video duration in milliseconds to MM:SS or HH:MM:SS format
 * 
 * @param durationInMilliseconds - Duration in milliseconds
 * @returns Formatted duration string
 */
export function formatVideoDurationFromMillis(durationInMilliseconds: number): string {
    const durationInSeconds = durationInMilliseconds / 1000;
    return formatVideoDuration(durationInSeconds);
}
