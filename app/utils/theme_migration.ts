// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import DatabaseManager from '@database/manager';

/**
 * Migrates existing themes in the database to include the new textSelectionColor property
 * This should be called when the app starts to ensure all cached themes have the new property
 */
export const migrateThemeTextSelectionColor = async () => {
    try {
        const {database} = DatabaseManager.getActiveServerDatabase() || {};
        if (!database) {
            console.log('No active database found for theme migration');
            return;
        }

        // Query all preference records that contain theme data
        const preferences = await database.get('Preference').query().fetch();
        
        for (const preference of preferences) {
            // Check if this is a theme preference
            if (preference.category === 'theme' || preference.name?.includes('theme')) {
                try {
                    const themeData = JSON.parse(preference.value);
                    
                    // Check if textSelectionColor is missing or needs updating
                    if (themeData && typeof themeData === 'object' && (!themeData.textSelectionColor || themeData.textSelectionColor === '#4CAF50')) {
                        // Update textSelectionColor to use buttonBg for consistency
                        themeData.textSelectionColor = themeData.buttonBg || '#00987e';
                        
                        // Update the preference in the database
                        await database.write(async () => {
                            await preference.update((record) => {
                                record.value = JSON.stringify(themeData);
                            });
                        });
                        
                        console.log(`Updated theme preference ${preference.name} with textSelectionColor`);
                    }
                } catch (parseError) {
                    // Skip non-JSON preferences
                    continue;
                }
            }
        }
        
        console.log('Theme migration completed successfully');
    } catch (error) {
        console.error('Error during theme migration:', error);
    }
};

/**
 * Forces a complete theme refresh by clearing theme cache
 * This can be used as a last resort to ensure themes are updated
 */
export const forceThemeRefresh = async () => {
    try {
        // This would need to be implemented based on how the app caches themes
        // For now, we'll just log that a refresh was requested
        console.log('Theme refresh requested - restart app to see changes');
        
        // You could also trigger a theme reload here if there's a mechanism for it
        // For example, if there's a theme context that can be refreshed
    } catch (error) {
        console.error('Error during theme refresh:', error);
    }
};
